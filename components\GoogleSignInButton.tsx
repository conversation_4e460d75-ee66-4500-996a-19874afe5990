import React from "react";
import {
  Pressable,
  Text,
  View,
  StyleSheet,
  ActivityIndicator,
  StyleProp,
  ViewStyle,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import globalStyles from "@/lib/globalStyles";
import { useTranslation } from "react-i18next";

type Props = {
  onPress?: () => void;
  isLoading?: boolean;
  style?: StyleProp<ViewStyle>;
  disabled?: boolean;
  variant?: "sign_in" | "sign_up";
};

const GoogleSignInButton = ({
  onPress,
  isLoading = false,
  style,
  disabled = false,
  variant = "sign_in",
}: Props) => {
  const { t } = useTranslation();

  const buttonText =
    variant === "sign_in"
      ? t("auth.sign_in_with_google")
      : t("auth.sign_up_with_google");

  return (
    <Pressable
      onPress={onPress}
      style={[styles.button, disabled && styles.disabled, style]}
      android_ripple={
        !disabled && !isLoading
          ? {
              color: "rgba(0, 0, 0, 0.1)",
              borderless: false,
            }
          : undefined
      }
      disabled={disabled || isLoading}
    >
      <View style={styles.content}>
        {isLoading ? (
          <ActivityIndicator
            size="small"
            color={globalStyles.colors.dark.secondary}
          />
        ) : (
          <>
            <View style={styles.iconContainer}>
              <Ionicons
                name="logo-google"
                size={20}
                color={globalStyles.colors.dark.secondary}
              />
            </View>
            <Text style={styles.text}>{buttonText}</Text>
          </>
        )}
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: globalStyles.colors.white,
    borderRadius: globalStyles.rounded.full,
    borderWidth: 1,
    borderColor: globalStyles.colors.light.secondary,
    paddingVertical: globalStyles.gap["2xs"],
    paddingHorizontal: globalStyles.gap.xs,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  disabled: {
    opacity: 0.6,
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  iconContainer: {
    marginRight: globalStyles.gap["2xs"],
  },
  text: {
    fontSize: globalStyles.size.lg,
    fontWeight: "500",
    color: globalStyles.colors.dark.secondary,
    marginTop: -2,
  },
});

export default GoogleSignInButton;
